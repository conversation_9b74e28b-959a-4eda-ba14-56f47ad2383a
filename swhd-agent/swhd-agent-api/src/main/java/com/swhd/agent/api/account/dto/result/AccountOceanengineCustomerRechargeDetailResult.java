package com.swhd.agent.api.account.dto.result;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeDetailResult对象")
public class AccountOceanengineCustomerRechargeDetailResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "广告主账号ID")
    private Long advertiserId;

    @Schema(description = "广告主账号名")
    private String advertiserName;

    @Schema(description = "代理商ID")
    private Long agentId;

    @Schema(description = "客户ID")
    private Long customId;

    @Schema(description = "充值账目金额")
    private BigDecimal rechargeAccountAmount;

    @Schema(description = "充值现金金额")
    private BigDecimal rechargeCashAmount;

    @Schema(description = "渠道返点")
    private BigDecimal channelRebate;

    @Schema(description = "剩余现金额度")
    private BigDecimal remainingCashLimit;

    @Schema(description = "执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "录入方式 WECHAT_BOT-微信机器人 MANUAL-手动录入  H5-h5页面")
    private String inputMethod;

    @Schema(description = "执行状态 PROCESSING-处理中 FAILED-处理失败 SUCCESS-处理成功")
    private String executeStatus;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "转账单号")
    private String transferSerial;

    @Schema(description = "充值消息")
    private String rechargeMessage;

    @Schema(description = "链路追踪ID")
    private String traceId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
