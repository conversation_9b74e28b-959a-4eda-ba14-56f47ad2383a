package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.MagiccubeHdLambdaQueryChainWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeDetailMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeDetailService;
import com.swj.magiccube.mp.util.SortOrderUtil;
import com.swj.magiccube.tool.bean.BeanUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自助充值明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeDetailServiceImpl extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeDetailMapper, AccountOceanengineCustomerRechargeDetail> implements AccountOceanengineCustomerRechargeDetailService {

    @Override
    public IPage<AccountOceanengineCustomerRechargeDetail> page(AccountOceanengineCustomerRechargeDetailPageParam param) {
        return buildQueryWrapper(BeanUtil.copy(param, AccountOceanengineCustomerRechargeDetailListParam.class))
                .page(convertToPage(param));
    }

    @Override
    public List<AccountOceanengineCustomerRechargeDetail> list(AccountOceanengineCustomerRechargeDetailListParam param) {
        return buildQueryWrapper(param)
                .list();
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return LambdaQueryWrapper
     */
    private MagiccubeHdLambdaQueryChainWrapper<AccountOceanengineCustomerRechargeDetail> buildQueryWrapper(AccountOceanengineCustomerRechargeDetailListParam param) {
        MagiccubeHdLambdaQueryChainWrapper<AccountOceanengineCustomerRechargeDetail> queryWrapper = lambdaQuery()
                .eq(Func.isNotEmpty(param.getAdvertiserId()), AccountOceanengineCustomerRechargeDetail::getAdvertiserId, param.getAdvertiserId())
                .eq(Func.isNotEmpty(param.getCustomId()), AccountOceanengineCustomerRechargeDetail::getCustomId, param.getCustomId())
                .betweenDateTimeList(param.getExecuteTimeBetween(), AccountOceanengineCustomerRechargeDetail::getExecuteTime)
                .in(Func.isNotEmpty(param.getInputMethods()), AccountOceanengineCustomerRechargeDetail::getInputMethod, param.getInputMethods())
                .in(Func.isNotEmpty(param.getExecuteStatuses()), AccountOceanengineCustomerRechargeDetail::getExecuteStatus, param.getExecuteStatuses())
                .eq(Func.isNotEmpty(param.getTransferSerial()), AccountOceanengineCustomerRechargeDetail::getTransferSerial, param.getTransferSerial());

        if (Func.isNotEmpty(param.getSort())) {
            String sqlSegment = SortOrderUtil.normalizeSqlSegment(param.getSort());
            queryWrapper.last("order by" + sqlSegment);
        } else {
            queryWrapper.orderByDesc(AccountOceanengineCustomerRechargeDetail::getExecuteTime)
                    .orderByDesc(AccountOceanengineCustomerRechargeDetail::getId);
        }

        return queryWrapper;
    }

}
