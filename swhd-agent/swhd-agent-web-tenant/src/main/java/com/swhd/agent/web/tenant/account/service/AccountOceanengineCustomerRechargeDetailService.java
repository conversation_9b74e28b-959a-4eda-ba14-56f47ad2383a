package com.swhd.agent.web.tenant.account.service;

import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeDetailResultVo;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.tool.Func;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeDetailService {

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    /**
     * 填充客户自助充值明细信息
     *
     * @param list 列表
     */
    public void fillDetailInfo(List<AccountOceanengineCustomerRechargeDetailResultVo> list) {
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(list, 
                AccountOceanengineCustomerRechargeDetailResultVo::getCustomId,
                AccountOceanengineCustomerRechargeDetailResultVo::setCustomInfo);
        
        // 设置账户信息
        accountOceanengineInfoService.setAccountInfo(list, false);
    }

}
