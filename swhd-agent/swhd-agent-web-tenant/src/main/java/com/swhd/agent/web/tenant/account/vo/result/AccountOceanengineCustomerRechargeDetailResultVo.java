package com.swhd.agent.web.tenant.account.vo.result;

import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineCustomerRechargeDetailResultVo对象")
public class AccountOceanengineCustomerRechargeDetailResultVo extends AccountOceanengineCustomerRechargeDetailResult implements AccountOceanengineInfoSet {

    @Schema(description = "客户信息")
    private CustomInfoResult customInfo;

    @Schema(description = "账户信息")
    private AccountOceanengineInfoResultVo account;

}
